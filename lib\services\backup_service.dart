/// 백업 및 복구 서비스
/// 
/// 동기화 작업 전 자동 백업과 문제 발생 시 롤백 기능을 제공합니다.
/// 데이터 무결성을 보장하고 잘못된 삭제로부터 보호합니다.
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 7월

import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/event.dart';
import '../models/product.dart';
import '../models/category.dart';
import '../models/seller.dart';
import '../models/sales_log.dart';
import '../models/prepayment.dart';
import '../models/prepayment_virtual_product.dart';
import '../models/prepayment_product_link.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';

/// 백업 데이터 타입
enum BackupDataType {
  events,
  products,
  categories,
  sellers,
  salesLogs,
  prepayments,
  prepaymentVirtualProducts,
  prepaymentProductLinks,
}

/// 백업 항목
class BackupItem {
  final String id;
  final BackupDataType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final int? eventId;

  const BackupItem({
    required this.id,
    required this.type,
    required this.data,
    required this.timestamp,
    this.eventId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'eventId': eventId,
    };
  }

  factory BackupItem.fromJson(Map<String, dynamic> json) {
    return BackupItem(
      id: json['id'],
      type: BackupDataType.values.firstWhere((e) => e.name == json['type']),
      data: json['data'],
      timestamp: DateTime.parse(json['timestamp']),
      eventId: json['eventId'],
    );
  }
}

/// 백업 세션
class BackupSession {
  final String sessionId;
  final DateTime timestamp;
  final List<BackupItem> items;
  final String operation;

  const BackupSession({
    required this.sessionId,
    required this.timestamp,
    required this.items,
    required this.operation,
  });

  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'timestamp': timestamp.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'operation': operation,
    };
  }

  factory BackupSession.fromJson(Map<String, dynamic> json) {
    return BackupSession(
      sessionId: json['sessionId'],
      timestamp: DateTime.parse(json['timestamp']),
      items: (json['items'] as List)
          .map((item) => BackupItem.fromJson(item))
          .toList(),
      operation: json['operation'],
    );
  }
}

/// 백업 및 복구 서비스
class BackupService {
  static const String _tag = 'BackupService';
  static const String _backupPrefix = 'backup_session_';
  static const int _maxBackupSessions = 10; // 최대 백업 세션 수
  static const int _backupRetentionDays = 7; // 백업 보관 기간 (일)

  final DatabaseService _databaseService;

  BackupService(this._databaseService);

  /// 백업 세션 시작
  Future<String> startBackupSession(String operation) async {
    final sessionId = 'backup_${DateTime.now().millisecondsSinceEpoch}';
    LoggerUtils.logInfo('백업 세션 시작: $sessionId (작업: $operation)', tag: _tag);
    return sessionId;
  }

  /// 데이터 백업 (삭제 전)
  Future<void> backupBeforeDelete({
    required String sessionId,
    required String operation,
    List<Event>? events,
    List<Product>? products,
    List<Category>? categories,
    List<Seller>? sellers,
    List<SalesLog>? salesLogs,
    List<Prepayment>? prepayments,
    List<PrepaymentVirtualProduct>? prepaymentVirtualProducts,
    List<PrepaymentProductLink>? prepaymentProductLinks,
  }) async {
    try {
      LoggerUtils.logInfo('데이터 백업 시작: $sessionId', tag: _tag);
      
      final backupItems = <BackupItem>[];
      final timestamp = DateTime.now();

      // 각 데이터 타입별 백업
      if (events != null) {
        for (final event in events) {
          backupItems.add(BackupItem(
            id: event.id.toString(),
            type: BackupDataType.events,
            data: event.toMap(),
            timestamp: timestamp,
          ));
        }
      }

      if (products != null) {
        for (final product in products) {
          backupItems.add(BackupItem(
            id: product.id.toString(),
            type: BackupDataType.products,
            data: product.toMap(),
            timestamp: timestamp,
            eventId: product.eventId,
          ));
        }
      }

      if (categories != null) {
        for (final category in categories) {
          backupItems.add(BackupItem(
            id: category.id.toString(),
            type: BackupDataType.categories,
            data: category.toMap(),
            timestamp: timestamp,
            eventId: category.eventId,
          ));
        }
      }

      if (sellers != null) {
        for (final seller in sellers) {
          backupItems.add(BackupItem(
            id: seller.id.toString(),
            type: BackupDataType.sellers,
            data: seller.toMap(),
            timestamp: timestamp,
            eventId: seller.eventId,
          ));
        }
      }

      if (salesLogs != null) {
        for (final salesLog in salesLogs) {
          backupItems.add(BackupItem(
            id: salesLog.id.toString(),
            type: BackupDataType.salesLogs,
            data: salesLog.toMap(),
            timestamp: timestamp,
            eventId: salesLog.eventId,
          ));
        }
      }

      if (prepayments != null) {
        for (final prepayment in prepayments) {
          backupItems.add(BackupItem(
            id: prepayment.id.toString(),
            type: BackupDataType.prepayments,
            data: prepayment.toMap(),
            timestamp: timestamp,
            eventId: prepayment.eventId,
          ));
        }
      }

      if (prepaymentVirtualProducts != null) {
        for (final virtualProduct in prepaymentVirtualProducts) {
          backupItems.add(BackupItem(
            id: virtualProduct.id.toString(),
            type: BackupDataType.prepaymentVirtualProducts,
            data: virtualProduct.toMap(),
            timestamp: timestamp,
            eventId: virtualProduct.eventId,
          ));
        }
      }

      if (prepaymentProductLinks != null) {
        for (final link in prepaymentProductLinks) {
          backupItems.add(BackupItem(
            id: link.id.toString(),
            type: BackupDataType.prepaymentProductLinks,
            data: link.toMap(),
            timestamp: timestamp,
            eventId: link.eventId,
          ));
        }
      }

      // 백업 세션 저장
      final session = BackupSession(
        sessionId: sessionId,
        timestamp: timestamp,
        items: backupItems,
        operation: operation,
      );

      await _saveBackupSession(session);
      
      LoggerUtils.logInfo('데이터 백업 완료: $sessionId (${backupItems.length}개 항목)', tag: _tag);
      
    } catch (e) {
      LoggerUtils.logError('데이터 백업 실패: $sessionId', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 백업 세션 저장
  Future<void> _saveBackupSession(BackupSession session) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '$_backupPrefix${session.sessionId}';
      final jsonString = jsonEncode(session.toJson());
      
      await prefs.setString(key, jsonString);
      
      // 오래된 백업 정리
      await _cleanupOldBackups();
      
    } catch (e) {
      LoggerUtils.logError('백업 세션 저장 실패: ${session.sessionId}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 오래된 백업 정리
  Future<void> _cleanupOldBackups() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_backupPrefix)).toList();
      
      // 백업 세션들을 시간순으로 정렬
      final sessions = <BackupSession>[];
      for (final key in keys) {
        try {
          final jsonString = prefs.getString(key);
          if (jsonString != null) {
            final session = BackupSession.fromJson(jsonDecode(jsonString));
            sessions.add(session);
          }
        } catch (e) {
          // 손상된 백업 세션 제거
          await prefs.remove(key);
        }
      }
      
      sessions.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      // 보관 기간 초과 또는 최대 개수 초과 백업 제거
      final cutoffDate = DateTime.now().subtract(const Duration(days: _backupRetentionDays));
      
      for (int i = 0; i < sessions.length; i++) {
        final session = sessions[i];
        if (i >= _maxBackupSessions || session.timestamp.isBefore(cutoffDate)) {
          final key = '$_backupPrefix${session.sessionId}';
          await prefs.remove(key);
          LoggerUtils.logDebug('오래된 백업 제거: ${session.sessionId}', tag: _tag);
        }
      }
      
    } catch (e) {
      LoggerUtils.logError('백업 정리 실패', tag: _tag, error: e);
    }
  }

  /// 백업에서 데이터 복구
  Future<bool> restoreFromBackup(String sessionId) async {
    try {
      LoggerUtils.logInfo('백업 복구 시작: $sessionId', tag: _tag);

      final session = await _getBackupSession(sessionId);
      if (session == null) {
        LoggerUtils.logError('백업 세션을 찾을 수 없음: $sessionId', tag: _tag);
        return false;
      }

      int restoredCount = 0;
      int errorCount = 0;

      // 데이터 타입별로 그룹화하여 복구
      final groupedItems = <BackupDataType, List<BackupItem>>{};
      for (final item in session.items) {
        groupedItems.putIfAbsent(item.type, () => []).add(item);
      }

      // 의존성 순서대로 복구 (Events -> Categories -> Products -> Sellers -> SalesLogs -> Prepayments -> ...)
      final restoreOrder = [
        BackupDataType.events,
        BackupDataType.categories,
        BackupDataType.products,
        BackupDataType.sellers,
        BackupDataType.prepaymentVirtualProducts,
        BackupDataType.prepaymentProductLinks,
        BackupDataType.prepayments,
        BackupDataType.salesLogs,
      ];

      for (final dataType in restoreOrder) {
        final items = groupedItems[dataType] ?? [];
        if (items.isEmpty) continue;

        LoggerUtils.logInfo('$dataType 복구 시작: ${items.length}개', tag: _tag);

        for (final item in items) {
          try {
            await _restoreItem(item);
            restoredCount++;
          } catch (e) {
            LoggerUtils.logError('항목 복구 실패: ${item.id} (${item.type})', tag: _tag, error: e);
            errorCount++;
          }
        }
      }

      LoggerUtils.logInfo('백업 복구 완료: $sessionId (복구: $restoredCount, 실패: $errorCount)', tag: _tag);
      return errorCount == 0;

    } catch (e) {
      LoggerUtils.logError('백업 복구 실패: $sessionId', tag: _tag, error: e);
      return false;
    }
  }

  /// 백업 세션 조회
  Future<BackupSession?> _getBackupSession(String sessionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '$_backupPrefix$sessionId';
      final jsonString = prefs.getString(key);

      if (jsonString == null) return null;

      return BackupSession.fromJson(jsonDecode(jsonString));
    } catch (e) {
      LoggerUtils.logError('백업 세션 조회 실패: $sessionId', tag: _tag, error: e);
      return null;
    }
  }

  /// 개별 항목 복구
  Future<void> _restoreItem(BackupItem item) async {
    switch (item.type) {
      case BackupDataType.events:
        final event = Event.fromMap(item.data);
        await _databaseService.insertOrUpdateEvent(event);
        break;

      case BackupDataType.products:
        final product = Product.fromMap(item.data);
        await _databaseService.insertOrUpdateProduct(product);
        break;

      case BackupDataType.categories:
        final category = Category.fromJson(item.data);
        await _databaseService.insertOrUpdateCategory(category);
        break;

      case BackupDataType.sellers:
        final seller = Seller.fromMap(item.data);
        await _databaseService.insertOrUpdateSeller(seller);
        break;

      case BackupDataType.salesLogs:
        final salesLog = SalesLog.fromMap(item.data);
        await _databaseService.insertOrUpdateSalesLog(salesLog);
        break;

      case BackupDataType.prepayments:
        final prepayment = Prepayment.fromMap(item.data);
        await _databaseService.insertOrUpdatePrepayment(prepayment);
        break;

      case BackupDataType.prepaymentVirtualProducts:
        final virtualProduct = PrepaymentVirtualProduct.fromMap(item.data);
        await _databaseService.insertOrUpdatePrepaymentVirtualProduct(virtualProduct);
        break;

      case BackupDataType.prepaymentProductLinks:
        final link = PrepaymentProductLink.fromMap(item.data);
        await _databaseService.insertOrUpdatePrepaymentProductLink(link);
        break;
    }
  }

  /// 사용 가능한 백업 세션 목록 조회
  Future<List<BackupSession>> getAvailableBackups() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_backupPrefix)).toList();

      final sessions = <BackupSession>[];
      for (final key in keys) {
        try {
          final jsonString = prefs.getString(key);
          if (jsonString != null) {
            final session = BackupSession.fromJson(jsonDecode(jsonString));
            sessions.add(session);
          }
        } catch (e) {
          LoggerUtils.logWarning('손상된 백업 세션 발견: $key', tag: _tag, error: e);
        }
      }

      // 최신순으로 정렬
      sessions.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      return sessions;
    } catch (e) {
      LoggerUtils.logError('백업 목록 조회 실패', tag: _tag, error: e);
      return [];
    }
  }

  /// 특정 백업 세션 삭제
  Future<void> deleteBackupSession(String sessionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '$_backupPrefix$sessionId';
      await prefs.remove(key);
      LoggerUtils.logInfo('백업 세션 삭제: $sessionId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('백업 세션 삭제 실패: $sessionId', tag: _tag, error: e);
    }
  }

  /// 모든 백업 삭제
  Future<void> clearAllBackups() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_backupPrefix)).toList();

      for (final key in keys) {
        await prefs.remove(key);
      }

      LoggerUtils.logInfo('모든 백업 삭제 완료: ${keys.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('모든 백업 삭제 실패', tag: _tag, error: e);
    }
  }
}
